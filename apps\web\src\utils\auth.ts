/**
 * Authentication utilities for apps/web
 * Independent implementation using PayloadCMS official authentication
 */

import type { LoginCredentials, LoginResponse } from '../types/auth';

const API_URL = process.env.NEXT_PUBLIC_API_URL || 'https://grandline-cms.vercel.app/api';

/**
 * Login user using PayloadCMS official authentication
 */
export async function login(credentials: LoginCredentials): Promise<LoginResponse> {
  const response = await fetch(`${API_URL}/users/login`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(credentials),
    credentials: 'include', // Essential for PayloadCMS HTTP-only cookies
  });

  const result = await response.json();

  if (!response.ok) {
    throw new Error(result.message || 'Login failed');
  }

  return result;
}

/**
 * Logout user using PayloadCMS official authentication
 */
export async function logout(): Promise<void> {
  const response = await fetch(`${API_URL}/users/logout`, {
    method: 'POST',
    credentials: 'include', // Essential for PayloadCMS HTTP-only cookies
    headers: {
      'Content-Type': 'application/json',
    },
  });

  if (!response.ok) {
    throw new Error('Logout failed');
  }
}

/**
 * Get current user using PayloadCMS official authentication
 */
export async function getCurrentUser() {
  const response = await fetch(`${API_URL}/users/me`, {
    method: 'GET',
    credentials: 'include', // Essential for PayloadCMS HTTP-only cookies
    headers: {
      'Content-Type': 'application/json',
    },
  });

  if (!response.ok) {
    throw new Error('Failed to get current user');
  }

  const userData = await response.json();
  return userData.user || userData;
}
