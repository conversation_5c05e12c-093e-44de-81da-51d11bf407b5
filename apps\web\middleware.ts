import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'

/**
 * ENTERPRISE MIDDLEWARE - PayloadCMS HTTP-only <PERSON><PERSON> Authentication
 * Follows PayloadCMS official authentication patterns with robust error handling
 */
export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  console.log('🔍 MIDDLEWARE: Processing request for:', pathname);

  // ALWAYS allow access to signin page - NO EXCEPTIONS
  if (pathname === '/signin') {
    console.log('✅ MIDDLEWARE: Allowing access to /signin page');
    return NextResponse.next();
  }

  // Allow access to auth-related pages
  if (pathname.startsWith('/(auth)') || pathname.includes('/signin')) {
    console.log('✅ MIDDLEWARE: Allowing access to auth page:', pathname);
    return NextResponse.next();
  }

  // Check for PayloadCMS HTTP-only cookies
  const payloadToken = request.cookies.get('payload-token');

  if (!payloadToken || !payloadToken.value) {
    console.log('❌ MIDDLEWARE: No PayloadCMS auth cookie found, redirecting to signin');
    console.log('Available cookies:', request.cookies.getAll().map(c => c.name));

    // Prevent redirect loops by checking if we're already redirecting
    if (request.nextUrl.searchParams.get('redirected') === 'true') {
      console.log('⚠️ MIDDLEWARE: Redirect loop detected, allowing access');
      return NextResponse.next();
    }

    const redirectUrl = new URL('/signin', request.url);
    redirectUrl.searchParams.set('redirected', 'true');
    redirectUrl.searchParams.set('from', pathname);
    return NextResponse.redirect(redirectUrl);
  }

  // 🛡️ ENTERPRISE SECURITY: Real-time PayloadCMS validation
  try {
    const apiUrl = process.env.NEXT_PUBLIC_API_URL || 'https://grandline-cms.vercel.app/api';

    // Use PayloadCMS standard authentication with HTTP-only cookies
    const response = await fetch(`${apiUrl}/users/me`, {
      method: 'GET',
      headers: {
        'Cookie': request.headers.get('cookie') || '',
        'Content-Type': 'application/json'
      }
    });

    if (response.ok) {
      const userData = await response.json();
      const user = userData.user || userData;

      // 🚨 Enterprise Security: Validate user role
      if (user && user.role !== 'trainee') {
        console.log('🚨 MIDDLEWARE SECURITY: User role changed to non-trainee, blocking access');
        console.log('Current role:', user.role);

        // Redirect to signin - PayloadCMS handles cookie management
        return NextResponse.redirect(new URL('/signin', request.url));
      }

      // 🚨 Enterprise Security: Validate account status
      if (user && !user.isActive) {
        console.log('🚨 MIDDLEWARE SECURITY: User account deactivated, blocking access');

        // Redirect to signin - PayloadCMS handles cookie management
        return NextResponse.redirect(new URL('/signin', request.url));
      }

      console.log('✅ MIDDLEWARE: PayloadCMS authentication and role validated, allowing access');
      return NextResponse.next();
    } else {
      console.log('❌ MIDDLEWARE: PayloadCMS token validation failed, redirecting to signin');
      return NextResponse.redirect(new URL('/signin', request.url));
    }
  } catch (error) {
    console.log('⚠️ MIDDLEWARE: PayloadCMS validation error, allowing access (AuthGuard will handle):', error);
    // If PayloadCMS API is down, let AuthGuard handle it to prevent blocking all access
    return NextResponse.next();
  }
}

export const config = {
  // ENTERPRISE: Exclude signin and Next.js internal routes from PayloadCMS authentication
  // Prevents redirect loops and allows static assets to load
  matcher: ['/', '/((?!signin|api|_next/static|_next/image|favicon.ico|.*\\..*).*)'],
}
