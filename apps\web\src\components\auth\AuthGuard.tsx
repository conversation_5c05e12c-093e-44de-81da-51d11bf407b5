'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/hooks/useAuth';
import { useLoading } from '@/components/loading';

interface AuthGuardProps {
  children: React.ReactNode;
}

/**
 * ENHANCED Authentication Guard with Real-Time Security Monitoring
 *
 * Shows content immediately and handles authentication in background.
 * Now includes real-time security alerts and instant logout on role changes.
 * Integrates with Facebook Meta-style loading screen for smooth UX.
 */
export function AuthGuard({ children }: AuthGuardProps) {
  const { user, loading, isAuthenticated, error } = useAuth();
  const { hideLoadingScreen, setProgress } = useLoading();
  const router = useRouter();
  const [hasRedirected, setHasRedirected] = useState(false);
  // Simplified authentication guard - trust PayloadCMS session management

  // Simplified authentication flow - trust PayloadCMS session management
  useEffect(() => {
    if (loading) {
      // Authentication is in progress, update loading progress
      console.log('🔄 AUTHGUARD: PayloadCMS authentication in progress...');
      setProgress(70);
    } else if (isAuthenticated && user) {
      // Authentication successful, hide loading screen
      console.log('✅ AUTHGUARD: PayloadCMS authentication successful, user:', user.email);
      setProgress(100);
      hideLoadingScreen();
    } else if (!loading && !isAuthenticated && !user && !hasRedirected) {
      // Authentication failed after loading completed, redirect to signin
      console.log('🔒 AUTHGUARD: PayloadCMS authentication failed, redirecting to signin');
      setProgress(100);
      hideLoadingScreen();
      setHasRedirected(true);

      // Redirect to signin
      setTimeout(() => {
        router.push('/signin');
      }, 200);
    }
  }, [loading, isAuthenticated, user, hasRedirected, router, setProgress, hideLoadingScreen]);

  // ALWAYS show content - no black screens!
  // If user is not authenticated, they'll be redirected anyway
  return <>{children}</>;
}
